#!/usr/bin/env python3
"""
Market Simulation Test
Simulates real market conditions with price movements and trading signals.
"""

import os
import time
import random
import pandas as pd
from datetime import datetime, timedelta

# Test Configuration
TEST_COIN = "WIFUSDT"
LOG_DIR = "./test_logs"

# Create test log directory
os.makedirs(LOG_DIR, exist_ok=True)

class MarketSimulator:
    def __init__(self):
        self.long = 0
        self.short = 0
        self.ROE = 0.0
        self.entryPrice = 1.0
        self.markPrice = 1.0
        self.Pnl = 0.0
        self.tradeqty = 100
        self.qty_2 = 50
        self.current_time = datetime.now()
        self.trade_log = []
        self.notifications = []
        
    def log(self, message, notification=False):
        timestamp = self.current_time.strftime("%Y-%m-%d %H:%M:%S")
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        
        if notification:
            self.notifications.append({
                'time': timestamp,
                'message': message,
                'roe': self.ROE,
                'price': self.markPrice
            })
    
    def flag_exists(self, flag_name):
        return os.path.exists(f"{LOG_DIR}/{TEST_COIN}_{flag_name}.txt")
    
    def create_flag(self, flag_name):
        with open(f"{LOG_DIR}/{TEST_COIN}_{flag_name}.txt", 'w') as f:
            f.write("")
    
    def remove_flag(self, flag_name):
        flag_path = f"{LOG_DIR}/{TEST_COIN}_{flag_name}.txt"
        if os.path.exists(flag_path):
            os.remove(flag_path)
    
    def cleanup_all_flags(self):
        """Clean up all flag files"""
        flag_patterns = [
            "tmp_flag_long", "tmp_flag_long_1", "tmp_flag_short", "tmp_flag_short_1",
            "tmp_flag_40", "tmp_flag_80", "tmp_flag_120", "reduction_40", "reduction_80", "reduction_120"
        ]
        for pattern in flag_patterns:
            self.remove_flag(pattern)
    
    def update_position(self, side, entry_price, mark_price):
        """Update position with new prices"""
        self.entryPrice = entry_price
        self.markPrice = mark_price
        
        if side == 'long':
            self.long = 1
            self.short = 2
            self.ROE = ((mark_price - entry_price) / entry_price) * 100
            self.Pnl = (mark_price - entry_price) * self.tradeqty
        elif side == 'short':
            self.long = 2
            self.short = 1
            self.ROE = ((entry_price - mark_price) / entry_price) * 100
            self.Pnl = (entry_price - mark_price) * self.tradeqty
        else:
            self.long = 0
            self.short = 0
            self.ROE = 0
            self.Pnl = 0
    
    def advance_time(self, minutes=3):
        """Advance simulation time"""
        self.current_time += timedelta(minutes=minutes)
    
    def apply_risk_management(self):
        """Apply all risk management logic"""
        # Position reduction logic
        if self.long == 1 and self.ROE < -2.5 and not self.flag_exists("tmp_flag_long"):
            self.create_flag("tmp_flag_long")
            self.log(f"⚠️ {TEST_COIN} LONG SIZE REDUCED", notification=True)
            self.log(f"• ROE: {self.ROE:.2f}% | PNL: ${self.Pnl:.2f} | Trigger: ROE below -2.5%")
        
        if self.short == 1 and self.ROE < -2.5 and not self.flag_exists("tmp_flag_short"):
            self.create_flag("tmp_flag_short")
            self.log(f"⚠️ {TEST_COIN} SHORT SIZE REDUCED", notification=True)
            self.log(f"• ROE: {self.ROE:.2f}% | PNL: ${self.Pnl:.2f} | Trigger: ROE below -2.5%")
        
        # Deep loss management
        if self.long == 1 and self.ROE < -16 and self.flag_exists("tmp_flag_long") and not self.flag_exists("tmp_flag_long_1"):
            self.create_flag("tmp_flag_long_1")
            self.log(f"📉 {TEST_COIN} LONG DEEP LOSS | ROE: {self.ROE:.2f}%")
        
        if self.short == 1 and self.ROE < -16 and self.flag_exists("tmp_flag_short") and not self.flag_exists("tmp_flag_short_1"):
            self.create_flag("tmp_flag_short_1")
            self.log(f"📉 {TEST_COIN} SHORT DEEP LOSS | ROE: {self.ROE:.2f}%")
        
        # Reset logic - Regular recovery
        if self.long == 1 and self.ROE > 3 and self.flag_exists("tmp_flag_long"):
            self.remove_flag("tmp_flag_long")
            self.log(f"✅ {TEST_COIN} LONG SIZE RESET", notification=True)
            self.log(f"• ROE: {self.ROE:.2f}% | Entry: ${self.entryPrice:.4f} | Reason: ROE above 3%")
        
        if self.short == 1 and self.ROE > 3 and self.flag_exists("tmp_flag_short"):
            self.remove_flag("tmp_flag_short")
            self.log(f"✅ {TEST_COIN} SHORT SIZE RESET", notification=True)
            self.log(f"• ROE: {self.ROE:.2f}% | Entry: ${self.entryPrice:.4f} | Reason: ROE above 3%")
        
        # Deep loss reset logic (with double reset prevention)
        if self.long == 1 and self.ROE > -10 and self.flag_exists("tmp_flag_long_1"):
            self.remove_flag("tmp_flag_long_1")
            self.remove_flag("tmp_flag_long")  # CRITICAL: Prevent double reset
            self.log(f"✅ {TEST_COIN} LONG SIZE RESET", notification=True)
            self.log(f"• ROE: {self.ROE:.2f}% | Entry: ${self.entryPrice:.4f} | Reason: ROE above -10%")
        
        if self.short == 1 and self.ROE > -10 and self.flag_exists("tmp_flag_short_1"):
            self.remove_flag("tmp_flag_short_1")
            self.remove_flag("tmp_flag_short")  # CRITICAL: Prevent double reset
            self.log(f"✅ {TEST_COIN} SHORT SIZE RESET", notification=True)
            self.log(f"• ROE: {self.ROE:.2f}% | Entry: ${self.entryPrice:.4f} | Reason: ROE above -10%")
    
    def apply_checkpoint_logic(self):
        """Apply checkpoint creation and TP logic"""
        # 40% Checkpoint
        if (self.long == 1 or self.short == 1) and self.ROE > 40 and not self.flag_exists("tmp_flag_40"):
            self.create_flag("tmp_flag_40")
            side_name = "LONG" if self.long == 1 else "SHORT"
            self.log(f"✅ {TEST_COIN} {side_name} CHECKPOINT 40", notification=True)
            self.log(f"• ROE: {self.ROE:.2f}% | Entry: ${self.entryPrice:.4f}")
        
        # 80% Checkpoint
        if (self.long == 1 or self.short == 1) and self.ROE > 80 and not self.flag_exists("tmp_flag_80"):
            if self.flag_exists("tmp_flag_40"):
                self.remove_flag("tmp_flag_40")
            self.create_flag("tmp_flag_80")
            side_name = "LONG" if self.long == 1 else "SHORT"
            self.log(f"✅ {TEST_COIN} {side_name} CHECKPOINT 80", notification=True)
        
        # Checkpoint TP triggers
        if self.long == 1 and self.ROE < 23 and self.flag_exists("tmp_flag_40"):
            self.remove_flag("tmp_flag_40")
            self.create_flag("reduction_40")
            self.log(f"⚠️ {TEST_COIN} LONG POSITION TP", notification=True)
            self.log(f"• ROE: {self.ROE:.2f}% | CheckPoint: 40% | Reason: ROE below 23%")
        
        if self.short == 1 and self.ROE < 23 and self.flag_exists("tmp_flag_40"):
            self.remove_flag("tmp_flag_40")
            self.create_flag("reduction_40")
            self.log(f"⚠️ {TEST_COIN} SHORT POSITION TP", notification=True)
            self.log(f"• ROE: {self.ROE:.2f}% | CheckPoint: 40% | Reason: ROE below 23%")

    def simulate_market_scenario(self, scenario_name, price_sequence, side, entry_price):
        """Simulate a specific market scenario"""
        self.log(f"\n🎯 Simulating: {scenario_name}")
        self.log("=" * 50)
        
        # Initialize position
        self.update_position(side, entry_price, entry_price)
        self.log(f"📍 {side.upper()} position opened at ${entry_price:.4f}")
        
        # Process price sequence
        for i, price in enumerate(price_sequence):
            self.advance_time(3)  # 3 minutes between updates
            self.update_position(side, entry_price, price)
            
            self.log(f"📊 Price: ${price:.4f} | ROE: {self.ROE:.2f}% | PNL: ${self.Pnl:.2f}")
            
            # Apply all logic
            self.apply_risk_management()
            self.apply_checkpoint_logic()
            
            # Small delay for readability
            time.sleep(0.1)

    def run_market_simulation(self):
        """Run comprehensive market simulation"""
        self.log("🌍 Starting Market Simulation")
        self.log("=" * 80)
        
        # Clean start
        self.cleanup_all_flags()
        
        # Scenario 1: LONG position with deep loss and recovery
        price_sequence_1 = [1.0, 0.975, 0.95, 0.82, 0.92, 1.04]  # -2.5%, -5%, -18%, -8%, +4%
        self.simulate_market_scenario("LONG Deep Loss Recovery", price_sequence_1, 'long', 1.0)
        
        self.cleanup_all_flags()
        self.advance_time(30)
        
        # Scenario 2: SHORT position with deep loss and recovery
        price_sequence_2 = [1.0, 1.025, 1.05, 1.18, 1.08, 0.96]  # -2.5%, -5%, -18%, -8%, +4%
        self.simulate_market_scenario("SHORT Deep Loss Recovery", price_sequence_2, 'short', 1.0)
        
        self.cleanup_all_flags()
        self.advance_time(30)
        
        # Scenario 3: LONG position with checkpoint progression
        price_sequence_3 = [1.0, 1.45, 1.85, 1.25, 1.55]  # +45%, +85%, +25%, +55%
        self.simulate_market_scenario("LONG Checkpoint Progression", price_sequence_3, 'long', 1.0)
        
        # Summary
        self.log("\n" + "=" * 80)
        self.log("📊 SIMULATION SUMMARY")
        self.log(f"Total notifications: {len(self.notifications)}")
        
        # Check for double resets
        reset_notifications = [n for n in self.notifications if "SIZE RESET" in n['message']]
        self.log(f"Total resets: {len(reset_notifications)}")
        
        # Group by time to detect double resets
        reset_times = {}
        for reset in reset_notifications:
            time_key = reset['time'][:16]  # Group by hour:minute
            if time_key not in reset_times:
                reset_times[time_key] = []
            reset_times[time_key].append(reset)
        
        double_resets = [time_key for time_key, resets in reset_times.items() if len(resets) > 1]
        
        if double_resets:
            self.log(f"⚠️ Double resets detected at: {double_resets}")
        else:
            self.log("✅ No double resets detected!")
        
        # Display all notifications
        self.log("\n📋 All Notifications:")
        for i, notif in enumerate(self.notifications, 1):
            self.log(f"{i}. [{notif['time']}] {notif['message']}")
        
        self.cleanup_all_flags()
        self.log("🏁 Market simulation completed!")

if __name__ == "__main__":
    simulator = MarketSimulator()
    simulator.run_market_simulation()
