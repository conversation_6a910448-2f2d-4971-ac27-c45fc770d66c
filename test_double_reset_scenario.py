#!/usr/bin/env python3
"""
Double Reset Scenario Test
Tests the exact scenario from user notifications to verify the fix is working.
"""

import os
import time
from datetime import datetime

# Test Configuration
TEST_COIN = "WIFUSDT"
LOG_DIR = "./test_logs"

# Create test log directory
os.makedirs(LOG_DIR, exist_ok=True)

class DoubleResetTester:
    def __init__(self):
        self.long = 0
        self.short = 0
        self.ROE = 0.0
        self.entryPrice = 0.0
        self.test_results = []
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def flag_exists(self, flag_name):
        return os.path.exists(f"{LOG_DIR}/{TEST_COIN}_{flag_name}.txt")
    
    def create_flag(self, flag_name):
        with open(f"{LOG_DIR}/{TEST_COIN}_{flag_name}.txt", 'w') as f:
            f.write("")
        self.log(f"Created flag: {flag_name}")
    
    def remove_flag(self, flag_name):
        flag_path = f"{LOG_DIR}/{TEST_COIN}_{flag_name}.txt"
        if os.path.exists(flag_path):
            os.remove(flag_path)
            self.log(f"Removed flag: {flag_name}")
    
    def cleanup_all_flags(self):
        """Clean up all flag files"""
        flag_patterns = [
            "tmp_flag_long", "tmp_flag_long_1", "tmp_flag_short", "tmp_flag_short_1"
        ]
        for pattern in flag_patterns:
            self.remove_flag(pattern)
    
    def set_position(self, side, roe, entry_price=0.8102891109314):
        """Set position state"""
        self.entryPrice = entry_price
        self.ROE = roe
        
        if side == 'long':
            self.long = 1
            self.short = 2
        elif side == 'short':
            self.long = 2
            self.short = 1
        else:
            self.long = 0
            self.short = 0
            
        self.log(f"Position: {side.upper()} | ROE: {roe:.2f}% | Entry: ${entry_price}")

    def test_exact_scenario(self):
        """Test the exact scenario from user notifications"""
        self.log("🧪 Testing Exact Double Reset Scenario", "TEST")
        self.log("=" * 60)
        
        # Clean start
        self.cleanup_all_flags()
        
        # Step 1: LONG position gets reduced (ROE < -2.5%)
        self.log("\n📉 Step 1: LONG position reduction")
        self.set_position('long', -3.0)
        
        if self.long == 1 and self.ROE < -2.5 and not self.flag_exists("tmp_flag_long"):
            self.create_flag("tmp_flag_long")
            self.log("⚠️ WIFUSDT LONG SIZE REDUCED", "NOTIFICATION")
            self.log(f"• ROE: {self.ROE:.2f}% | Trigger: ROE below -2.5%")
        
        # Step 2: Position goes into deep loss (ROE < -16%)
        self.log("\n📉 Step 2: Deep loss scenario")
        self.set_position('long', -18.0)
        
        if self.long == 1 and self.ROE < -16 and self.flag_exists("tmp_flag_long"):
            self.create_flag("tmp_flag_long_1")
            self.log("📉 Deep loss flag created")
        
        # Step 3: Recovery from deep loss (ROE > -10%) - First reset
        self.log("\n📈 Step 3: Recovery from deep loss")
        self.set_position('long', -7.82, 0.8102891109314)
        
        reset_count = 0
        if self.long == 1 and self.ROE > -10 and self.flag_exists("tmp_flag_long_1"):
            self.remove_flag("tmp_flag_long_1")
            # CRITICAL FIX: Also remove regular flag to prevent double reset
            self.remove_flag("tmp_flag_long")
            reset_count += 1
            self.log("✅ WIFUSDT LONG SIZE RESET", "NOTIFICATION")
            self.log(f"• Time: 2025-09-03 - Wed 00:45:13")
            self.log(f"• ROE: {self.ROE:.2f}%")
            self.log(f"• Entry Price: ${self.entryPrice}")
            self.log(f"• Reason: ROE above -10%")
            self.log("🔧 FIXED: Removed both flags to prevent double reset")
        
        # Step 4: Further recovery (ROE > 3%) - Should NOT trigger second reset
        self.log("\n📈 Step 4: Further recovery (testing double reset prevention)")
        self.set_position('long', 3.75, 0.8048059841663)
        
        if self.long == 1 and self.ROE > 3 and self.flag_exists("tmp_flag_long"):
            reset_count += 1
            self.log("❌ WIFUSDT LONG SIZE RESET", "ERROR")
            self.log(f"• Time: 2025-09-03 - Wed 00:48:13")
            self.log(f"• ROE: {self.ROE:.2f}%")
            self.log(f"• Entry Price: ${self.entryPrice}")
            self.log(f"• Reason: ROE above 3%")
            self.log("🚨 DOUBLE RESET DETECTED - FIX FAILED!")
        else:
            self.log("✅ No second reset triggered - Fix working correctly!")
        
        # Test Results
        self.log("\n" + "=" * 60)
        self.log("📊 TEST RESULTS", "RESULT")
        self.log(f"Total resets triggered: {reset_count}")
        
        if reset_count == 1:
            self.log("✅ SUCCESS: Double reset prevented!", "SUCCESS")
            self.test_results.append("PASS")
        else:
            self.log("❌ FAILURE: Double reset still occurring!", "FAILURE")
            self.test_results.append("FAIL")
        
        return reset_count == 1

    def test_short_scenario(self):
        """Test the same scenario for SHORT positions"""
        self.log("\n🧪 Testing SHORT Position Double Reset Prevention", "TEST")
        self.log("=" * 60)
        
        # Clean start
        self.cleanup_all_flags()
        
        # Step 1: SHORT position reduction
        self.log("\n📉 Step 1: SHORT position reduction")
        self.set_position('short', -3.0)
        
        if self.short == 1 and self.ROE < -2.5 and not self.flag_exists("tmp_flag_short"):
            self.create_flag("tmp_flag_short")
            self.log("⚠️ WIFUSDT SHORT SIZE REDUCED", "NOTIFICATION")
        
        # Step 2: Deep loss
        self.log("\n📉 Step 2: Deep loss scenario")
        self.set_position('short', -18.0)
        
        if self.short == 1 and self.ROE < -16 and self.flag_exists("tmp_flag_short"):
            self.create_flag("tmp_flag_short_1")
            self.log("📉 Deep loss flag created")
        
        # Step 3: Recovery from deep loss
        self.log("\n📈 Step 3: Recovery from deep loss")
        self.set_position('short', -7.82)
        
        reset_count = 0
        if self.short == 1 and self.ROE > -10 and self.flag_exists("tmp_flag_short_1"):
            self.remove_flag("tmp_flag_short_1")
            self.remove_flag("tmp_flag_short")  # Prevent double reset
            reset_count += 1
            self.log("✅ WIFUSDT SHORT SIZE RESET", "NOTIFICATION")
            self.log(f"• ROE: {self.ROE:.2f}% | Reason: ROE above -10%")
        
        # Step 4: Further recovery
        self.log("\n📈 Step 4: Further recovery")
        self.set_position('short', 3.75)
        
        if self.short == 1 and self.ROE > 3 and self.flag_exists("tmp_flag_short"):
            reset_count += 1
            self.log("❌ DOUBLE RESET DETECTED!", "ERROR")
        else:
            self.log("✅ No double reset - Fix working!")
        
        # Results
        if reset_count == 1:
            self.log("✅ SHORT double reset prevention: PASS", "SUCCESS")
            self.test_results.append("PASS")
        else:
            self.log("❌ SHORT double reset prevention: FAIL", "FAILURE")
            self.test_results.append("FAIL")
        
        return reset_count == 1

    def test_cross_contamination(self):
        """Test that LONG and SHORT flags don't interfere with each other"""
        self.log("\n🧪 Testing Cross-Contamination Prevention", "TEST")
        self.log("=" * 60)
        
        # Clean start
        self.cleanup_all_flags()
        
        # Create SHORT flag
        self.set_position('short', -3.0)
        self.create_flag("tmp_flag_short")
        self.log("Created SHORT flag")
        
        # Switch to LONG position - should not trigger LONG reset
        self.set_position('long', 4.0)
        
        if self.long == 1 and self.ROE > 3 and self.flag_exists("tmp_flag_long"):
            self.log("❌ LONG reset triggered by SHORT flag!", "ERROR")
            self.test_results.append("FAIL")
        else:
            self.log("✅ LONG not affected by SHORT flag", "SUCCESS")
            self.test_results.append("PASS")
        
        # Test reverse scenario
        self.create_flag("tmp_flag_long")
        self.set_position('short', 4.0)
        
        if self.short == 1 and self.ROE > 3 and self.flag_exists("tmp_flag_short"):
            self.remove_flag("tmp_flag_short")
            self.log("✅ SHORT reset triggered by correct flag", "SUCCESS")
        else:
            self.log("❌ SHORT reset not working", "ERROR")

    def run_all_tests(self):
        """Run all double reset tests"""
        self.log("🚀 Starting Double Reset Prevention Tests")
        self.log("=" * 80)
        
        # Run tests
        test1 = self.test_exact_scenario()
        test2 = self.test_short_scenario()
        self.test_cross_contamination()
        
        # Final summary
        self.log("\n" + "=" * 80)
        self.log("🏁 FINAL TEST SUMMARY", "SUMMARY")
        
        passed = self.test_results.count("PASS")
        failed = self.test_results.count("FAIL")
        
        self.log(f"Tests passed: {passed}")
        self.log(f"Tests failed: {failed}")
        
        if failed == 0:
            self.log("🎉 ALL TESTS PASSED - Double reset fix is working!", "SUCCESS")
        else:
            self.log("⚠️ Some tests failed - Fix needs more work", "WARNING")
        
        # Cleanup
        self.cleanup_all_flags()
        self.log("🧹 Cleaned up all test flags")

if __name__ == "__main__":
    tester = DoubleResetTester()
    tester.run_all_tests()
