#!/usr/bin/env python3
"""
Test Runner
Executes all trading logic tests and provides a comprehensive report.
"""

import os
import subprocess
import sys
from datetime import datetime

def run_test(test_file, test_name):
    """Run a single test file"""
    print(f"\n{'='*60}")
    print(f"🧪 Running {test_name}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run([sys.executable, test_file], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(result.stdout)
            return True, result.stdout
        else:
            print(f"❌ Test failed with return code {result.returncode}")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False, result.stderr
    except subprocess.TimeoutExpired:
        print("⏰ Test timed out after 60 seconds")
        return False, "Timeout"
    except Exception as e:
        print(f"💥 Test crashed: {e}")
        return False, str(e)

def main():
    """Run all tests"""
    print("🚀 Trading Logic Test Suite")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # Test configurations
    tests = [
        ("test_trading_logic.py", "Comprehensive Trading Logic Test"),
        ("test_double_reset_scenario.py", "Double Reset Prevention Test"),
        ("test_market_simulation.py", "Market Simulation Test")
    ]
    
    results = []
    
    # Run each test
    for test_file, test_name in tests:
        if os.path.exists(test_file):
            success, output = run_test(test_file, test_name)
            results.append((test_name, success, output))
        else:
            print(f"⚠️ Test file {test_file} not found")
            results.append((test_name, False, "File not found"))
    
    # Summary report
    print("\n" + "="*80)
    print("📊 TEST SUITE SUMMARY")
    print("="*80)
    
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    print("\n📋 Individual Test Results:")
    for test_name, success, output in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} - {test_name}")
    
    # Detailed analysis
    print("\n🔍 Detailed Analysis:")
    
    # Check for double reset prevention
    double_reset_test = next((r for r in results if "Double Reset" in r[0]), None)
    if double_reset_test and double_reset_test[1]:
        if "ALL TESTS PASSED" in double_reset_test[2]:
            print("✅ Double reset prevention: WORKING")
        else:
            print("⚠️ Double reset prevention: NEEDS ATTENTION")
    
    # Check for flag management
    comprehensive_test = next((r for r in results if "Comprehensive" in r[0]), None)
    if comprehensive_test and comprehensive_test[1]:
        if "All flags cleaned up properly" in comprehensive_test[2]:
            print("✅ Flag management: WORKING")
        else:
            print("⚠️ Flag management: NEEDS ATTENTION")
    
    # Check for market simulation
    market_test = next((r for r in results if "Market" in r[0]), None)
    if market_test and market_test[1]:
        if "No double resets detected" in market_test[2]:
            print("✅ Market simulation: NO DOUBLE RESETS")
        else:
            print("⚠️ Market simulation: DOUBLE RESETS DETECTED")
    
    # Final verdict
    print("\n" + "="*80)
    if passed == total:
        print("🎉 ALL TESTS PASSED - Trading logic fixes are working correctly!")
        print("✅ Safe to deploy the updated orderexec.py")
    else:
        print("⚠️ Some tests failed - Review the fixes before deployment")
        print("❌ Do not deploy until all tests pass")
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create test report
    with open("test_report.txt", "w") as f:
        f.write(f"Trading Logic Test Report\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("="*50 + "\n\n")
        
        f.write(f"Summary: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)\n\n")
        
        for test_name, success, output in results:
            f.write(f"Test: {test_name}\n")
            f.write(f"Result: {'PASS' if success else 'FAIL'}\n")
            f.write(f"Output:\n{output}\n")
            f.write("-"*50 + "\n\n")
    
    print("📄 Detailed report saved to test_report.txt")

if __name__ == "__main__":
    main()
