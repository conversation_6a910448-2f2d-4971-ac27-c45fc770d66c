if trading_enabled == 1:

    long = 0
    short = 0

    for i in positions:
        symbol_name = i["info"]['symbol']
        side = i['side']
        tradeamount = i["info"]['isolatedMargin']
        position_amt = float(i["info"]['positionAmt'])
        tradeqty = abs(position_amt)
        entryPrice = i["info"]['entryPrice']
        markPrice = i["info"]['markPrice']
        Pnl = i["info"]['unRealizedProfit']
        #ROE = i['percentage']
        ROE = round(float(i['percentage']), 2)    

        if side == 'long':
            long = 1
            short = 2
        elif side == 'short':
            long = 2
            short = 1

    if (long == 1 or short == 1):
        print(f"{formatted_time} - [POSITION] ROE: {ROE}%")

        # Example if Pnl is a string like "$123.45" or "1,234.56"
        cleaned_Pnl = Pnl.replace(',', '').replace('$', '')
        Pnl_float = float(cleaned_Pnl)
        print(f"{formatted_time} - [POSITION] PNL: ${Pnl_float:.2f}")
                    
    # New Buy Position
    if (test_010['Buy_F_017'].iloc[-2] > 0) and (long == 0 or long == 1):
        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")                
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt")


        border1 = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty)
        message = (f"🔵 *{Coin} LONG SIGNAL*\n\n"
                  f"📊 *Trade Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Direction: LONG\n\n"
                  f"Position successfully established. 📈")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)

        print(f"{formatted_time} - [TRADE] Long position opened successfully")
        time.sleep(2)

    # New Sell Position
    if (test_010['Sell_F_017'].iloc[-2] > 0) and (short == 0 or short == 1):
        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")        

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")                
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt")            

        sorder1 = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty)
        message = (f"🔴 *{Coin} SHORT SIGNAL*\n\n"
                  f"📊 *Trade Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Direction: SHORT\n\n"
                  f"Position successfully established. 📉")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)

        print(f"{formatted_time} - [TRADE] Short position opened successfully")
        time.sleep(2)

    # Close Long and Open Short
    if test_010['Sell_F_017'].iloc[-2] > 0 and long == 1:

        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt") 

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")                
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt")

        # Close buy order
        sorder1 = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=tradeqty)
        message = (f"🔄 *{Coin} POSITION SWITCH*\n\n"
                  f"📊 *Trade Summary:*\n"
                  f"• Time: {current_time}\n"
                  f"• Long Position Closed: ${price}\n"
                  f"• Short Position Opened: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Closed ROE: {ROE:.2f}%\n"
                  f"• Realized PNL: ${float(Pnl):.2f}\n\n"
                  f"Position successfully switched from LONG to SHORT. 🔄")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - [TRADE] Long position closed for reversal")
        time.sleep(2)

        # Open Sell order
        border2 = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty)
        message = (f"🔴 *{Coin} SHORT SIGNAL*\n\n"
                  f"📊 *Trade Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Direction: SHORT\n\n"
                  f"New short position successfully established. 📉")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - [TRADE] Short position opened after reversal")

        # Reset position data after switch to prevent old ROE from triggering checkpoints
        exchange.load_markets()
        positions = exchange.fetch_positions(symbols=[symbol])

        # Reset position variables
        long = 0
        short = 0

        # Recalculate position data for the new SHORT position
        for i in positions:
            symbol_name = i["info"]['symbol']
            side = i['side']
            tradeamount = i["info"]['isolatedMargin']
            position_amt = float(i["info"]['positionAmt'])
            tradeqty = abs(position_amt)
            entryPrice = i["info"]['entryPrice']
            markPrice = i["info"]['markPrice']
            Pnl = i["info"]['unRealizedProfit']
            ROE = round(float(i['percentage']), 2)

            if side == 'long':
                long = 1
                short = 2
            elif side == 'short':
                long = 2
                short = 1

    # Close Short and Open Long
    if test_010['Buy_F_017'].iloc[-2] > 0 and short == 1:
        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt") 

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")             
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt")

        # Close Sell order
        bborder1 = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=tradeqty)
        message = (f"🔄 *{Coin} POSITION SWITCH*\n\n"
                  f"📊 *Trade Summary:*\n"
                  f"• Time: {current_time}\n"
                  f"• Short Position Closed: ${price}\n"
                  f"• Long Position Opened: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Closed ROE: {ROE:.2f}%\n"
                  f"• Realized PNL: ${float(Pnl):.2f}\n\n"
                  f"Position successfully switched from SHORT to LONG. 🔄")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - [TRADE] Short position closed for reversal")
        time.sleep(2)


        # Open Buy order
        bborder2 = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty)
        message = (f"🔵 *{Coin} LONG SIGNAL*\n\n"
                  f"📊 *Trade Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Direction: LONG\n\n"
                  f"New long position successfully established. 📈")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - [TRADE] Long position opened after reversal")

        # Reset position data after switch to prevent old ROE from triggering checkpoints
        exchange.load_markets()
        positions = exchange.fetch_positions(symbols=[symbol])

        # Reset position variables
        long = 0
        short = 0

        # Recalculate position data for the new LONG position
        for i in positions:
            symbol_name = i["info"]['symbol']
            side = i['side']
            tradeamount = i["info"]['isolatedMargin']
            position_amt = float(i["info"]['positionAmt'])
            tradeqty = abs(position_amt)
            entryPrice = i["info"]['entryPrice']
            markPrice = i["info"]['markPrice']
            Pnl = i["info"]['unRealizedProfit']
            ROE = round(float(i['percentage']), 2)

            if side == 'long':
                long = 1
                short = 2
            elif side == 'short':
                long = 2
                short = 1

    # Take Profit - Short Position
    if test_010['Buy_F_018'].iloc[-2] > 0 and short == 1:


        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")

        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")                 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")   

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt")

        try:
            stpclose = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=tradeqty)
            message = (f"🛑 *{Coin} SHORT POSITION CLOSED*\n\n"
                    f"📊 *Exit Summary:*\n"
                    f"• Exit Time: {current_time}\n"
                    f"• Entry Price: ${entryPrice}\n"
                    f"• Exit Price: ${price}\n"
                    f"• Position Size: {tradeqty}\n"
                    f"• Final ROE: {ROE:.2f}%\n"
                    f"• Realized PNL: ${float(Pnl):.2f}\n\n"
                    f"Stop-loss order executed successfully. �")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [STOP-LOSS] Short position closed successfully")
            time.sleep(2)


        except Exception as e:
            print(f"{formatted_time} - [ERROR] Short position stop-loss failed: {str(e)}")

    # Take Profit - Long Position
    if test_010['Sell_F_018'].iloc[-2] > 0 and long == 1:


        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")                
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")  

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt")

        try:
            btpclose = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=tradeqty)
            message = (f"🛑 *{Coin} LONG POSITION CLOSED*\n\n"
                    f"📊 *Exit Summary:*\n"
                    f"• Exit Time: {current_time}\n"
                    f"• Entry Price: ${entryPrice}\n"
                    f"• Exit Price: ${price}\n"
                    f"• Position Size: {tradeqty}\n"
                    f"• Final ROE: {ROE:.2f}%\n"
                    f"• Realized PNL: ${float(Pnl):.2f}\n\n"
                    f"Stop-loss order executed successfully. �")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [STOP-LOSS] Long position closed successfully")
            time.sleep(2)

            # Remove flag file if exists
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")


        except Exception as e:
            print(f"{formatted_time} - [ERROR] Long position stop-loss failed: {str(e)}")

#new risk Management Logic

    # Risk Management
    #Reset Short Position Size
    if short == 1 and ROE > 6 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
        Adjust_S_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty_2)

        # Send detailed Telegram message
        message = (f"✅ *{Coin} SHORT SIZE RESTORED*\n\n"
           f"📊 *Recovery Details:*\n"
           f"• Time: {current_time}\n"
           f"• Current ROE: {ROE:.2f}%\n"
           f"• Entry Price: ${entryPrice}\n"
           f"• Trigger: ROE above 6%\n\n"
           f"Position size successfully restored. 📈")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - [RISK-MGMT] Short position size restored successfully")
        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")

    #Reset Long Position Size
    if long == 1 and ROE > 6 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):

        Adjust_b_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty_2)

        # Send detailed Telegram message
        message = (f"✅ *{Coin} LONG SIZE RESTORED*\n\n"
                   f"📊 *Recovery Details:*\n"
                   f"• Time: {current_time}\n"
                   f"• Current ROE: {ROE:.2f}%\n"
                   f"• Entry Price: ${entryPrice}\n"
                   f"• Trigger: ROE above 6%\n\n"
                   f"Position size successfully restored. 📈")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)

        print(f"{formatted_time} - [RISK-MGMT] Long position size restored successfully")

        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")

    #Risk Management

    End = 6
    Start = 0
    if (long == 1 or short == 1) and ROE < 15 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
        print(f"{formatted_time} - [SYSTEM] Risk Management Mode")
        while Start < End:

            exchange.load_markets ()
            positions = exchange.fetch_positions(symbols=[symbol])

            # Do something..
            #print(f"{formatted_time} - Iteration {Start + 1}")
            
            long = 0
            short = 0

            for i in positions:
                symbol_name = i["info"]['symbol']
                side = i['side']
                tradeamount = i["info"]['isolatedMargin']
                position_amt = float(i["info"]['positionAmt'])
                tradeqty = abs(position_amt)
                entryPrice = i["info"]['entryPrice']
                markPrice = i["info"]['markPrice']
                Pnl = i["info"]['unRealizedProfit']
                #ROE = i['percentage']
                ROE = round(float(i['percentage']), 2)    

                if side == 'long':
                    long = 1
                    short = 2
                elif side == 'short':
                    long = 2
                    short = 1
            
            #print(f"{formatted_time} - S_ROE_{Start + 1}: {ROE}")    

            if (long == 1 or short == 1):
                print(f"{formatted_time} - [RISK-MGMT] Cycle {Start + 1}/6 - ROE: {ROE}%")

            #print(type(ROE), ROE)

            # Short Position Size Management
            if short == 1 and ROE < -2.5 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):

                # Determine reduction quantity based on size adjustment
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                    reduction_qty = (qty_2 + qty_3)  # Reduce the incremented amount
                else:
                    reduction_qty = qty_2  # Reduce only base amount

                reduce_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=reduction_qty)
                
                # Send detailed Telegram message about position reduction
                message = (f"⚠️ *{Coin} SHORT SIZE REDUCED*\n\n"
                          f"📊 *Risk Management Details:*\n"
                          f"• Time: {current_time}\n"
                          f"• Current ROE: {ROE:.2f}%\n"
                          f"• Partial PNL: ${float(Pnl):.2f}\n"
                          f"• Trigger: ROE below -2.5%\n\n"
                          f"Risk management protocol activated. 🛡️")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [RISK-MGMT] Short position size reduced due to negative ROE")
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt")

                # Create flag file
                if not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                    flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"
                    open(flag_file, 'w').close()

            #Reset Short Position Size
            if short == 1 and ROE > 3 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                Adjust_S_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty_2)

                # Send detailed Telegram message
                message = (f"✅ *{Coin} SHORT SIZE RESTORED*\n\n"
                   f"📊 *Recovery Details:*\n"
                   f"• Time: {current_time}\n"
                   f"• Current ROE: {ROE:.2f}%\n"
                   f"• Entry Price: ${entryPrice}\n"
                   f"• Trigger: ROE above 3%\n\n"
                   f"Position size successfully restored. 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [RISK-MGMT] Short position size restored after recovery")
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")

            # Short Position Size Management
            if short == 1 and ROE < -16 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                print(f"{formatted_time} - [RISK-MGMT] Short position entering deep loss zone (-16%)")
                
                # Create flag file
                if not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
                    flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"
                    open(flag_file, 'w').close()                

            #Reset Short Position Size    
            if short == 1 and ROE > -10 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
                Adjust_S_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty_2)
                
                # Send detailed Telegram message
                message = (f"✅ *{Coin} SHORT SIZE RESET*\n"
                   f"• Time: {current_time}\n"
                   f"• ROE: {ROE:.2f}%\n"
                   f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                   f"• Reason: ROE above -10%\n\n"
                   f"Position recovered! 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [RISK-MGMT] Short position size restored from deep loss")
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")


            # Long Position Size Management
            if long == 1 and ROE < -2.5 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):

                # Determine reduction quantity based on size adjustment
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):

                    reduction_qty = (qty_2 + qty_3)  # Reduce the incremented amount
                else:
                    reduction_qty = qty_2  # Reduce only base amount

                reduce_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=reduction_qty)
                # Send detailed Telegram message about position reduction
                message = (f"⚠️ *{Coin} LONG SIZE REDUCED*\n\n"
                          f"📊 *Risk Management Details:*\n"
                          f"• Time: {current_time}\n"
                          f"• Current ROE: {ROE:.2f}%\n"
                          f"• Partial PNL: ${float(Pnl):.2f}\n"
                          f"• Trigger: ROE below -2.5%\n\n"
                          f"Risk management protocol activated. 🛡️")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [RISK-MGMT] Long position size reduced due to negative ROE")

                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt")

                # Create flag file
                if not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                    flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"
                    open(flag_file, 'w').close()

            #Reset Long Position Size
            if long == 1 and ROE > 3 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                
                Adjust_b_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty_2)
                
                # Send detailed Telegram message
                message = (f"✅ *{Coin} LONG SIZE RESET*\n"
                           f"• Time: {current_time}\n"
                           f"• ROE: {ROE:.2f}%\n"
                           f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                           f"• Reason: ROE above 3%\n\n"
                           f"Position recovered! 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [RISK-MGMT] Long position size restored after recovery")
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")


            # Short Position Size Management
            if long == 1 and ROE < -16 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                print(f"{formatted_time} - [RISK-MGMT] Long position entering deep loss zone (-16%)")

                # Create flag file
                if not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
                    flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"
                    open(flag_file, 'w').close()


            #Reset Long Position Size
            if long == 1 and ROE > -10 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
                
                Adjust_b_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty_2)
                
                # Send detailed Telegram message
                message = (f"✅ *{Coin} LONG SIZE RESET*\n"
                           f"• Time: {current_time}\n"
                           f"• ROE: {ROE:.2f}%\n"
                           f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                           f"• Reason: ROE above -10%\n\n"
                           f"Position recovered! 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [RISK-MGMT] Long position size restored from deep loss")
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

               # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")  

            # Size Adjustment   
            #Adjust Long size.
            
            if long == 1 and ROE > 9 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                adjust_long_size = binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=qty_3)
                message = (f"📈 *{Coin} LONG SIZE INCREASED*\n\n"
                          f"📊 *Enhancement Details:*\n"
                          f"• Time: {current_time}\n"
                          f"• Current ROE: {ROE:.2f}%\n"
                          f"• Additional Size: {qty_3}\n"
                          f"• Trigger: ROE above 9%\n\n"
                          f"Position size successfully increased. �")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [SIZE-BOOST] Long position size increased due to high ROE")
                open(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt", 'w').close()
            #Adjust Long size.

            if short == 1 and ROE > 9 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                adjust_short_size = binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=qty_3)
                message = (f"📉 *{Coin} SHORT SIZE INCREASED*\n\n"
                          f"📊 *Enhancement Details:*\n"
                          f"• Time: {current_time}\n"
                          f"• Current ROE: {ROE:.2f}%\n"
                          f"• Additional Size: {qty_3}\n"
                          f"• Trigger: ROE above 9%\n\n"
                          f"Position size successfully increased. �")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [SIZE-BOOST] Short position size increased due to high ROE")
                open(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt", 'w').close()



            # Wait for 30 seconds
            time.sleep(20)            
            # Increment the Start
            Start += 1


    # Take profit 

    # Checkpoint creattion
    if not (os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt") or
           os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_60.txt") or
           os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt") or
           os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")):

        # Checkpoint 40% logic
        if (long == 1 or short == 1) and ROE > 40 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt") and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_60.txt") and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            # Clean up any existing lower flags (none for 40%)

            if long == 1:
                trade_type = 'LONG'
            elif short == 1:
                trade_type = 'SHORT'

            print(f"{formatted_time} - [CHECKPOINT] {trade_type} position reached 40% ROE - checkpoint established")
            # Send detailed Telegram message
            message = (f"🎯 *{Coin} {trade_type} CHECKPOINT ESTABLISHED*\n\n"
                       f"📊 *Checkpoint Details:*\n"
                       f"• Time: {current_time}\n"
                       f"• Current ROE: {ROE:.2f}%\n"
                       f"• Entry Price: ${entryPrice}\n"
                       f"• Checkpoint Level: 40%\n"
                       f"• Take-Profit Trigger: 26%\n\n"
                       f"Profit protection checkpoint activated. 🎯")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)

            flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"
            open(flag_file, 'w').close()


        # Checkpoint 60% logic
        if (long == 1 or short == 1) and ROE > 60 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_60.txt") and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            # Clean up any existing lower flags (none for 60%)

            if long == 1:
                trade_type = 'LONG'
            elif short == 1:
                trade_type = 'SHORT'

            print(f"{formatted_time} - [CHECKPOINT] {trade_type} position reached 60% ROE - checkpoint established")
            # Send detailed Telegram message
            message = (f"✅ *{Coin} {trade_type} CHECKPOINT 60*\n"
                       f"• Time: {current_time}\n"
                       f"• ROE: {ROE:.2f}%\n"
                       f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                       f"• Reason: ROE above 60%\n"
                       f"• Profit Booked: below 46%\n\n"
                       f"• Check Point Created! 📈")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)

            # Delete 40% flag if it exists
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")

            # Create 60% flag
            flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_60.txt"
            open(flag_file, 'w').close()


        # Checkpoint 80% logic
        if (long == 1 or short == 1) and ROE > 80 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):

            if long == 1:
                trade_type = 'LONG'
            elif short == 1:
                trade_type = 'SHORT'

            print(f"{formatted_time} - [CHECKPOINT] {trade_type} position reached 80% ROE - checkpoint established")
            # Send detailed Telegram message
            message = (f"✅ *{Coin} {trade_type} CHECKPOINT 80*\n"
                       f"• Time: {current_time}\n"
                       f"• ROE: {ROE:.2f}%\n"
                       f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                       f"• Reason: ROE above 80%\n"
                       f"• Profit Booked: below 55%\n\n"
                       f"• Check Point Created! 📈")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            
            # Delete both 40% and 60% flags if they exist
            for checkpoint in ["40", "60"]:
                flag_path = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_{checkpoint}.txt"
                if os.path.exists(flag_path):
                    os.remove(flag_path)                

            # Create 80% flag
            flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"
            open(flag_file, 'w').close()

        # Checkpoint 120% logic
        if (long == 1 or short == 1) and ROE > 120 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):

            if long == 1:
                trade_type = 'LONG'
            elif short == 1:
                trade_type = 'SHORT'

            print(f"{formatted_time} - [CHECKPOINT] {trade_type} position reached 120% ROE - checkpoint established")
            # Send detailed Telegram message
            message = (f"✅ *{Coin} {trade_type} CHECKPOINT 120*\n"
                       f"• Time: {current_time}\n"
                       f"• ROE: {ROE:.2f}%\n"
                       f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                       f"• Reason: ROE above 120%\n"
                       f"• Profit Booked: below 90%\n\n"
                       f"• Check Point Created! 📈")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)


            # Delete both 40% and 80% flags if they exist
            for checkpoint in ["40","60","80"]:
                flag_path = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_{checkpoint}.txt"
                if os.path.exists(flag_path):
                    os.remove(flag_path)
            # Create 120% flag
            flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"
            open(flag_file, 'w').close()

        # ========== REDUCTION TRIGGERS ==========
        
        # 40% Checkpoint Reduction
        if long == 1 and ROE < 26 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            # Check if size increment was applied and adjust reduction quantity accordingly
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                reduction_qty = (qty_2 + qty_3)  # Reduce the incremented amount
            else:
                reduction_qty = qty_2  # Reduce only base amount

  
            reduce_order = binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=reduction_qty)
            message = (f"💰 *{Coin} LONG TAKE-PROFIT*\n\n"
                      f"📊 *Profit Summary:*\n"
                      f"• Time: {current_time}\n"
                      f"• Final ROE: {ROE:.2f}%\n"
                      f"• Realized PNL: ${float(Pnl):.2f}\n"
                      f"• Checkpoint: 40%\n"
                      f"• Trigger: ROE below 26%\n\n"
                      f"Take-profit executed. Position size reduced. �")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [TAKE-PROFIT] Long position partially closed from 40% checkpoint")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt", 'w').close()

        if short == 1 and ROE < 26 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            # Check if size increment was applied and adjust reduction quantity accordingly
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                reduction_qty = (qty_2 + qty_3)  # Reduce the incremented amount
            else:
                reduction_qty = qty_2  # Reduce only base amount

            reduce_order = binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=reduction_qty)
            message = (f"⚠️ *{Coin} SHORT POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 40%\n"
                      f"• Reason: ROE below 26%\n\n"
                      f"TP Triggered, size Reduced. Reversal Monitoring On 🛡️")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [TAKE-PROFIT] Short position partially closed from 40% checkpoint")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt", 'w').close()


        # 60% Checkpoint Reduction
        if long == 1 and ROE < 46 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_60.txt"):
            # Check if size increment was applied and adjust reduction quantity accordingly
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                reduction_qty = (qty_2 + qty_3)  # Reduce the incremented amount
            else:
                reduction_qty = qty_2  # Reduce only base amount


            close_order = binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=reduction_qty)
            message = (f"⚠️ *{Coin} LONG POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 60%\n"
                      f"• Reason: ROE below 46%\n\n"
                      f"Take-Profit Triggered 🎯")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [TAKE-PROFIT] Long position partially closed from 60% checkpoint")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_60.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_60.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_60.txt", 'w').close()

        if short == 1 and ROE < 46 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_60.txt"):
            # Check if size increment was applied and adjust reduction quantity accordingly
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                reduction_qty = (qty_2 + qty_3)  # Reduce the incremented amount
            else:
                reduction_qty = qty_2  # Reduce only base amount


                reduce_order = binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=reduction_qty)
            message = (f"⚠️ *{Coin} SHORT POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 60%\n"
                      f"• Reason: ROE below 46%\n\n"
                      f"Take-Profit Triggered 🎯")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [TAKE-PROFIT] Short position partially closed from 60% checkpoint")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_60.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_60.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_60.txt", 'w').close()


        # 80% Checkpoint Reduction
        if long == 1 and ROE < 55 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            # Check if size increment was applied and adjust reduction quantity accordingly
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                reduction_qty = (qty_2 + qty_3)  # Reduce the incremented amount
            else:
                reduction_qty = qty_2  # Reduce only base amount
            
            close_order = binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=reduction_qty)
            message = (f"⚠️ *{Coin} LONG POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 80%\n"
                      f"• Reason: ROE below 55%\n\n"
                      f"Take-Profit Triggered 🎯")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [TAKE-PROFIT] Long position partially closed from 80% checkpoint")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt", 'w').close()

        if short == 1 and ROE < 55 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            # Check if size increment was applied and adjust reduction quantity accordingly
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                reduction_qty = (qty_2 + qty_3)  # Reduce the incremented amount
            else:
                reduction_qty = qty_2  # Reduce only base amount

            reduce_order = binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=reduction_qty)
            message = (f"⚠️ *{Coin} SHORT POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 80%\n"
                      f"• Reason: ROE below 55%\n\n"
                      f"Take-Profit Triggered 🎯")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [TAKE-PROFIT] Short position partially closed from 80% checkpoint")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt", 'w').close()

        # 120% Checkpoint Reduction
        if long == 1 and ROE < 90 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            # Check if size increment was applied and adjust reduction quantity accordingly
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                reduction_qty = (qty_2 + qty_3)  # Reduce the incremented amount
            else:
                reduction_qty = qty_2  # Reduce only base amount

            reduce_order = binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=reduction_qty)
            message = (f"⚠️ *{Coin} LONG POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 120%\n"
                      f"• Reason: ROE below 90%\n\n"
                      f"Take-Profit Triggered 🎯")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [TAKE-PROFIT] Long position partially closed from 120% checkpoint")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt", 'w').close()

        if short == 1 and ROE < 90 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            # Check if size increment was applied and adjust reduction quantity accordingly
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                reduction_qty = (qty_2 + qty_3)  # Reduce the incremented amount
            else:
                reduction_qty = qty_2  # Reduce only base amount


            reduce_order = binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=reduction_qty)
            message = (f"⚠️ *{Coin} SHORT POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 120%\n"
                      f"• Reason: ROE below 90%\n\n"
                      f"Take-Profit Triggered 🎯")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [TAKE-PROFIT] Short position partially closed from 120% checkpoint")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt", 'w').close()

    #Reversal monitoring mode
    End = 6
    Start = 0
    if (long == 1 or short == 1) and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
        print(f"{formatted_time} - [SYSTEM] Reversal Monitoring Mode")
        while Start < End:

            exchange.load_markets ()
            positions = exchange.fetch_positions(symbols=[symbol])

            # Do something..
            #print(f"{formatted_time} - Iteration {Start + 1}")
            
            long = 0
            short = 0

            for i in positions:
                symbol_name = i["info"]['symbol']
                side = i['side']
                tradeamount = i["info"]['isolatedMargin']
                position_amt = float(i["info"]['positionAmt'])
                tradeqty = abs(position_amt)
                entryPrice = i["info"]['entryPrice']
                markPrice = i["info"]['markPrice']
                Pnl = i["info"]['unRealizedProfit']
                #ROE = i['percentage']
                ROE = round(float(i['percentage']), 2)    

                if side == 'long':
                    long = 1
                    short = 2
                elif side == 'short':
                    long = 2
                    short = 1
            
            #print(f"{formatted_time} - S_ROE_{Start + 1}: {ROE}")    
            

            if (long == 1 or short == 1):
                print(f"{formatted_time} - [REVERSAL] Monitoring cycle {Start + 1}/6 - ROE: {ROE}%")

            #print(type(ROE), ROE)

            # Short Position Size Management
            if short == 1 and ROE < 8 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt") and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                print(f"{formatted_time} - [REVERSAL] Short position declining below 8% - monitoring activated")
                # Create flag file
                flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"
                open(flag_file, 'w').close()

                if long == 1:
                    trade_type = 'LONG' 
                elif short == 1:
                    trade_type = 'SHORT'
                # Send detailed Telegram message
                message = (f"✅ *{Coin} {trade_type} CHECKPOINT REDUCTION 8*\n"
                           f"• Time: {current_time}\n"
                           f"• ROE: {ROE:.2f}%\n"
                           f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                           f"• Reason: ROE Reduce After TP \n"
                           f"• Status: Reversal Monitory ON, waiting for ROE >13% to scale\n\n"
                           f"• Check Point Created! 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)


            #Reset Short Position Size    
            if short == 1 and ROE > 13 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")

                Adjust_S_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty_2)
                
                # Send detailed Telegram message
                message = (f"✅ *{Coin} SHORT SIZE RESET*\n"
                   f"• Time: {current_time}\n"
                   f"• ROE: {ROE:.2f}%\n"
                   f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                   f"• Reason: Reduction CheckPoint 8%\n"
                   f"• Reason: ROE above 13%\n\n"
                   f"• Position Reversal Found, recovered 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [REVERSAL] Short position recovered above 13%")


            # Short Position Size Management
            if long == 1 and ROE < 8 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt") and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                print(f"{formatted_time} - [REVERSAL] Long position declining below 8% - monitoring activated")

                # Create flag file
                flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"
                open(flag_file, 'w').close()

                if long == 1:
                    trade_type = 'LONG' 
                elif short == 1:
                    trade_type = 'SHORT'
                # Send detailed Telegram message
                message = (f"✅ *{Coin} {trade_type} CHECKPOINT REDUCTION 8*\n"
                           f"• Time: {current_time}\n"
                           f"• ROE: {ROE:.2f}%\n"
                           f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                           f"• Reason: ROE Reduce After TP \n"
                           f"• Status: Reversal Monitory ON, waiting for ROE >13% to scale\n\n"
                           f"• Check Point Created! 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)


            #Reset Long Position Size
            if long == 1 and ROE > 13 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")            
                
                Adjust_b_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty_2)
                
                # Send detailed Telegram message
                message = (f"✅ *{Coin} LONG SIZE RESET*\n"
                           f"• Time: {current_time}\n"
                           f"• ROE: {ROE:.2f}%\n"
                           f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                           f"• Reason: Reduction CheckPoint 8%\n"
                           f"• Reason: ROE above 13%\n\n"
                           f"• Position Reversal Found, recovered 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [REVERSAL] Long position recovered above 13%")

            #Close positions after decline from checkpoint 8
            if (long == 1) and ROE < -5 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                # Close buy order
                sorder1 = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=tradeqty)
                message = (f"🔄 *{Coin} Long Position Closed*\n\n"
                           f"📊 *Position Details:*\n"
                           f"• Time: {current_time}\n"
                           f"• Entry Price: ${price}\n"
                           f"• Quantity: {qty}\n"
                           f"• Return on Equity (ROE): {ROE:.2f}%\n"
                           f"• Profit & Loss (PNL): ${float(Pnl):.2f}\n"
                           f"• Reason: ROE decreased below -5%\n"
                           f"• ROE declined from the checkpoint of 8% after 23% reduction monitoring period.\n")

                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)        
                print(f"{formatted_time} - [REVERSAL] Long position closed due to continued decline")
                time.sleep(2)

                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")

                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")                      

            if (short == 1) and ROE < -5 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                # Close Sell order
                bborder1 = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=tradeqty) 
                message = (f"🔄 *{Coin} Short Position Closed*\n\n"
                           f"📊 *Position Details:*\n"
                           f"• Time: {current_time}\n"
                           f"• Entry Price: ${price}\n"
                           f"• Quantity: {qty}\n"
                           f"• Return on Equity (ROE): {ROE:.2f}%\n"
                           f"• Profit & Loss (PNL): ${float(Pnl):.2f}\n"
                           f"• Reason: ROE decreased below -5%\n"
                           f"• ROE declined from the checkpoint of 8% after 23% reduction monitoring period.\n")

                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)        
                print(f"{formatted_time} - [REVERSAL] Short position closed due to continued decline")

                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")

                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")
            

            # Wait for 30 seconds
            time.sleep(20)            
            # Increment the Start
            Start += 1
