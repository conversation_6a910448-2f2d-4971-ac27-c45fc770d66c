if trading_enabled == 1:

    long = 0
    short = 0

    for i in positions:
        symbol_name = i["info"]['symbol']
        side = i['side']
        tradeamount = i["info"]['isolatedMargin']
        position_amt = float(i["info"]['positionAmt'])
        tradeqty = abs(position_amt)
        entryPrice = i["info"]['entryPrice']
        markPrice = i["info"]['markPrice']
        Pnl = i["info"]['unRealizedProfit']
        #ROE = i['percentage']
        ROE = round(float(i['percentage']), 2)    

        if side == 'long':
            long = 1
            short = 2
        elif side == 'short':
            long = 2
            short = 1

    if (long == 1 or short == 1):
        print(f"{formatted_time} - Trade_ROE: {ROE}")

        # Example if Pnl is a string like "$123.45" or "1,234.56"
        cleaned_Pnl = Pnl.replace(',', '').replace('$', '')
        Pnl_float = float(cleaned_Pnl)        
        print(f"{formatted_time} - Trade_PNL: {Pnl_float:.2f}")
                    
    # New Buy Position
    if (test_010['Buy_F_017'].iloc[-2] > 0) and (long == 0 or long == 1):
        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")                
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")

        border1 = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty)    
        message = (f"🔵 *{Coin} BUY SIGNAL*\n\n"
                  f"📊 *Entry Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Quantity: {qty}\n"
                  f"• Side: BUY\n\n"
                  f"Good luck with your trading! 📈")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)

        print(f"{formatted_time} - Buy Order")
        time.sleep(2)

    # New Sell Position
    if (test_010['Sell_F_017'].iloc[-2] > 0) and (short == 0 or short == 1):
        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")        

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")                
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")

        sorder1 = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty)
        message = (f"🔴 *{Coin} SELL SIGNAL*\n\n"
                  f"📊 *Entry Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Quantity: {qty}\n"
                  f"• Side: SELL\n\n"
                  f"Good luck with your trading! 📉")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)

        print(f"{formatted_time} - SELL Order")
        time.sleep(2)
    
    # Close Long and Open Short
    if test_010['Sell_F_017'].iloc[-2] > 0 and long == 1:

        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt") 

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")                
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")


        # Close buy order
        sorder1 = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=tradeqty)
        message = (f"🔄 *{Coin} POSITION SWITCH*\n\n"
                  f"📊 *Position Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Closed Long at: ${price}\n"
                  f"• Opened Short at: ${price}\n"
                  f"• Quantity: {qty}\n"
                  f"• ROE (Closed): {ROE:.2f}%\n"
                  f"• PNL (Closed): ${float(Pnl):.2f}\n\n"
                  f"Switched from LONG to SHORT position 🔄")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)        
        print(f"{formatted_time} - Close buy Order")
        time.sleep(2)

        # Open Sell order
        border2 = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty)
        message = (f"🔴 *{Coin} SELL SIGNAL*\n\n"
                  f"📊 *Entry Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Quantity: {qty}\n"
                  f"• Side: SELL\n\n"
                  f"Good luck with your trading! 📉")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - Open Sell Order")           

    # Close Short and Open Long
    if test_010['Buy_F_017'].iloc[-2] > 0 and short == 1:
        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt") 

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")             
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")


        # Close Sell order
        bborder1 = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=tradeqty) 
        message = (f"🔄 *{Coin} POSITION SWITCH*\n\n"
                  f"📊 *Position Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Closed Short at: ${price}\n"
                  f"• Opened Long at: ${price}\n"
                  f"• Quantity: {qty}\n"
                  f"• ROE (Closed): {ROE:.2f}%\n"
                  f"• PNL (Closed): ${float(Pnl):.2f}\n\n"
                  f"Switched from LONG to SHORT position 🔄")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)        
        print(f"{formatted_time} - Close Sell Order")         
        time.sleep(2)
        

        # Open Buy order
        bborder2 = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty) 
        message = (f"🔵 *{Coin} BUY SIGNAL*\n\n"
                  f"📊 *Entry Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Quantity: {qty}\n"
                  f"• Side: BUY\n\n"
                  f"Good luck with your trading! 📈")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - Open Buy Order")     

    # Take Profit - Short Position
    if test_010['Buy_F_018'].iloc[-2] > 0 and short == 1:


        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")

        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")                 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")   


        try:
            stpclose = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=tradeqty)    
            message = (f"✅ *{Coin} SHORT POSITION CLOSED - STOP LOSS*\n\n"
                    f"📊 *Position Details:*\n"
                    f"• Exit Time: {current_time}\n"
                    f"• Entry Price: ${entryPrice}\n"
                    f"• Exit Price: ${price}\n"
                    f"• Quantity: {tradeqty}\n"
                    f"• ROE: {ROE:.2f}%\n"
                    f"• PNL: ${float(Pnl):.2f}\n\n"
                    f"Stop Loss triggered! 🚨")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)   
            print(f"{formatted_time} - Sell Stop")                            
            time.sleep(2)
             

        except Exception as e:
            print(f"{formatted_time} - Error in short position take profit: {str(e)}")

    # Take Profit - Long Position
    if test_010['Sell_F_018'].iloc[-2] > 0 and long == 1:


        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")                
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") 
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt")             

        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")  
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")  

        try:
            btpclose = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=tradeqty)
            message = (f"✅ *{Coin} LONG POSITION CLOSED - STOP LOSS*\n\n"
                    f"📊 *Position Details:*\n"
                    f"• Exit Time: {current_time}\n"
                    f"• Entry Price: ${entryPrice}\n"
                    f"• Exit Price: ${price}\n"
                    f"• Quantity: {tradeqty}\n"
                    f"• ROE: {ROE:.2f}%\n"
                    f"• PNL: ${float(Pnl):.2f}\n\n"
                    f"Stop Loss triggered! 🚨")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - Buy Stop")          
            time.sleep(2)

            # Remove flag file if exists
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")


        except Exception as e:
            print(f"{formatted_time} - Error in long position take profit: {str(e)}")            

#new risk Management Logic

    # Risk Management
    #Reset Short Position Size    
    if short == 1 and ROE > 6 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
        Adjust_S_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty_2)
        
        # Send detailed Telegram message
        message = (f"✅ *{Coin} SHORT POSITION SIZE RESET*\n"
           f"• Time: {current_time}\n"
           f"• ROE: {ROE:.2f}%\n"
           f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
           f"• Reason: ROE above 10%\n\n"
           f"Position recovered! 📈")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - Short Size Reset")
        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")

    #Reset Long Position Size
    if long == 1 and ROE > 6 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
        
        Adjust_b_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty_2)
        
        # Send detailed Telegram message
        message = (f"✅ *{Coin} LONG POSITION SIZE RESET*\n"
                   f"• Time: {current_time}\n"
                   f"• ROE: {ROE:.2f}%\n"
                   f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                   f"• Reason: ROE above 10%\n\n"
                   f"Position recovered! 📈")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)

        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")

    #Risk Management        
    
    End = 6
    Start = 0        
    if (long == 1 or short == 1) and ROE < 12 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
        print(f"{formatted_time} - Mode: Risk Management")
        while Start < End:

            exchange.load_markets ()
            positions = exchange.fetch_positions(symbols=[symbol])

            # Do something..
            #print(f"{formatted_time} - Iteration {Start + 1}")
            
            long = 0
            short = 0

            for i in positions:
                symbol_name = i["info"]['symbol']
                side = i['side']
                tradeamount = i["info"]['isolatedMargin']
                position_amt = float(i["info"]['positionAmt'])
                tradeqty = abs(position_amt)
                entryPrice = i["info"]['entryPrice']
                markPrice = i["info"]['markPrice']
                Pnl = i["info"]['unRealizedProfit']
                #ROE = i['percentage']
                ROE = round(float(i['percentage']), 2)    

                if side == 'long':
                    long = 1
                    short = 2
                elif side == 'short':
                    long = 2
                    short = 1
            
            #print(f"{formatted_time} - S_ROE_{Start + 1}: {ROE}")    

            if (long == 1 or short == 1):
                print(f"{formatted_time} - S_ROE_{Start + 1}: {ROE}")

            #print(type(ROE), ROE)

            # Short Position Size Management
            if short == 1 and ROE < -5 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                
                close_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty_2)
                # Send detailed Telegram message about position reduction
                message = (f"⚠️ *{Coin} SHORT POSITION SIZE REDUCTION*\n\n"
                          f"📊 *Position Details:*\n"
                          f"• Time: {current_time}\n"
                          f"• ROE: {ROE:.2f}%\n"
                          f"• PNL (Closed): ${float(Pnl):.2f}\n"
                          f"• Reason: ROE below -5%\n\n"
                          f"Risk management activated! 🛡️")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - Short Size Reducetion")
                # Create flag file
                flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"
                open(flag_file, 'w').close()

            #Reset Short Position Size    
            if short == 1 and ROE > -1 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                Adjust_S_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty_2)
                
                # Send detailed Telegram message
                message = (f"✅ *{Coin} SHORT POSITION SIZE RESET*\n"
                   f"• Time: {current_time}\n"
                   f"• ROE: {ROE:.2f}%\n"
                   f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                   f"• Reason: ROE above -1%\n\n"
                   f"Position recovered! 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - Short Size Reset")
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")


            # Short Position Size Management
            if short == 1 and ROE < -16 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                
                # Create flag file
                flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"
                open(flag_file, 'w').close()

            #Reset Short Position Size    
            if short == 1 and ROE > -10 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
                Adjust_S_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty_2)
                
                # Send detailed Telegram message
                message = (f"✅ *{Coin} SHORT POSITION SIZE RESET*\n"
                   f"• Time: {current_time}\n"
                   f"• ROE: {ROE:.2f}%\n"
                   f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                   f"• Reason: ROE above -10%\n\n"
                   f"Position recovered! 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - Short Size Reset")
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")


            # Short Position Size Management
            if long == 1 and ROE < -5 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):

                close_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty_2)
                # Send detailed Telegram message about position reduction
                message = (f"⚠️ *{Coin} LONG POSITION SIZE REDUCTION*\n\n"
                          f"📊 *Position Details:*\n"
                          f"• Time: {current_time}\n"
                          f"• ROE: {ROE:.2f}%\n"
                          f"• PNL (Closed): ${float(Pnl):.2f}\n"                  
                          f"• Reason: ROE below -5%\n\n"
                          f"Risk management activated! 🛡️")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - Long Size Reduction")
                # Create flag file
                flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"
                open(flag_file, 'w').close()

            #Reset Long Position Size
            if long == 1 and ROE > -1 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                
                Adjust_b_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty_2)
                
                # Send detailed Telegram message
                message = (f"✅ *{Coin} LONG POSITION SIZE RESET*\n"
                           f"• Time: {current_time}\n"
                           f"• ROE: {ROE:.2f}%\n"
                           f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                           f"• Reason: ROE above -1%\n\n"
                           f"Position recovered! 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - Long Size Reset")
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")


            # Short Position Size Management
            if long == 1 and ROE < -16 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):

                # Create flag file
                flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"
                open(flag_file, 'w').close()

            #Reset Long Position Size
            if long == 1 and ROE > -10 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
                
                Adjust_b_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty_2)
                
                # Send detailed Telegram message
                message = (f"✅ *{Coin} LONG POSITION SIZE RESET*\n"
                           f"• Time: {current_time}\n"
                           f"• ROE: {ROE:.2f}%\n"
                           f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                           f"• Reason: ROE above -10%\n\n"
                           f"Position recovered! 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - Long Size Reset")
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_1.txt")

               # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")                    

            # Wait for 30 seconds
            time.sleep(20)            
            # Increment the Start
            Start += 1


    # Take profit 

    # Only proceed if no reduction has been made yet
    if not (os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt") or
           os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt") or
           os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt")):

        # Checkpoint 40% logic
        if (long == 1 or short == 1) and ROE > 40 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt") and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            # Clean up any existing lower flags (none for 40%)

            if long == 1:
                trade_type = 'LONG' 
            elif short == 1:
                trade_type = 'SHORT'
            # Send detailed Telegram message
            message = (f"✅ *{Coin} {trade_type} CHECKPOINT 40*\n"
                       f"• Time: {current_time}\n"
                       f"• ROE: {ROE:.2f}%\n"
                       f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                       f"• Reason: ROE above 40%\n"
                       f"• Profit Booked: below 23%\n\n"
                       f"• Check Point Created! 📈")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)

            flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"
            open(flag_file, 'w').close()

        # Checkpoint 80% logic
        if (long == 1 or short == 1) and ROE > 80 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt") and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):

            if long == 1:
                trade_type = 'LONG' 
            elif short == 1:
                trade_type = 'SHORT'
            # Send detailed Telegram message
            message = (f"✅ *{Coin} {trade_type} CHECKPOINT 80*\n"
                       f"• Time: {current_time}\n"
                       f"• ROE: {ROE:.2f}%\n"
                       f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                       f"• Reason: ROE above 80%\n"
                       f"• Profit Booked: below 55%\n\n"
                       f"• Check Point Created! 📈")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)

            # Delete 40% flag if it exists
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
            # Create 80% flag
            flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"
            open(flag_file, 'w').close()

        # Checkpoint 120% logic
        if (long == 1 or short == 1) and ROE > 120 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            
            if long == 1:
                trade_type = 'LONG' 
            elif short == 1:
                trade_type = 'SHORT'
            # Send detailed Telegram message
            message = (f"✅ *{Coin} {trade_type} CHECKPOINT 120*\n"
                       f"• Time: {current_time}\n"
                       f"• ROE: {ROE:.2f}%\n"
                       f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                       f"• Reason: ROE above 120%\n"
                       f"• Profit Booked: below 95%\n\n"
                       f"• Check Point Created! 📈")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)


            # Delete both 40% and 80% flags if they exist
            for checkpoint in ["40", "80"]:
                flag_path = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_{checkpoint}.txt"
                if os.path.exists(flag_path):
                    os.remove(flag_path)
            # Create 120% flag
            flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"
            open(flag_file, 'w').close()

        # ========== REDUCTION TRIGGERS ==========
        
        # 40% Checkpoint Reduction
        if long == 1 and ROE < 23 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            close_order = binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=qty_2)
            message = (f"⚠️ *{Coin} LONG POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 40%\n"
                      f"• Reason: ROE below 23%\n\n"
                      f"TP Triggered, size Reduced. Reversal Monitoring On 🛡️")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - Size Reduction from Check Point 40")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt", 'w').close()

        if short == 1 and ROE < 23 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
            close_order = binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=qty_2)
            message = (f"⚠️ *{Coin} SHORT POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 40%\n"
                      f"• Reason: ROE below 23%\n\n"
                      f"TP Triggered, size Reduced. Reversal Monitoring On 🛡️")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - Short Size Reduction")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt", 'w').close()

        # 80% Checkpoint Reduction
        if long == 1 and ROE < 55 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            close_order = binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=qty_2)
            message = (f"⚠️ *{Coin} LONG POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 80%\n"
                      f"• Reason: ROE below 55%\n\n"
                      f"Take-Profit Triggered 🎯")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - Long Size Reduction")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt", 'w').close()

        if short == 1 and ROE < 55 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
            close_order = binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=qty_2)
            message = (f"⚠️ *{Coin} SHORT POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 80%\n"
                      f"• Reason: ROE below 55%\n\n"
                      f"Take-Profit Triggered 🎯")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - Short Size Reduction")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_80.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_80.txt", 'w').close()

        # 120% Checkpoint Reduction
        if long == 1 and ROE < 95 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            close_order = binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=qty_2)
            message = (f"⚠️ *{Coin} LONG POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 120%\n"
                      f"• Reason: ROE below 95%\n\n"
                      f"Take-Profit Triggered 🎯")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - Long Size Reduction")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt", 'w').close()

        if short == 1 and ROE < 95 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
            close_order = binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=qty_2)
            message = (f"⚠️ *{Coin} SHORT POSITION TP*\n\n"
                      f"📊 *Position Details:*\n"
                      f"• Time: {current_time}\n"
                      f"• ROE: {ROE:.2f}%\n"
                      f"• PNL (Closed): ${float(Pnl):.2f}\n"
                      f"• CheckPoint: 120%\n"
                      f"• Reason: ROE below 95%\n\n"
                      f"Take-Profit Triggered 🎯")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - Short Size Reduction")
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_120.txt")
            open(f"/root/02_implementation/03_app_logs/{Coin}_reduction_120.txt", 'w').close()                

    #Reversal monitoring mode        
    End = 6
    Start = 0        
    if (long == 1 or short == 1) and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
        print(f"{formatted_time} - Mode: Reversel ")
        while Start < End:

            exchange.load_markets ()
            positions = exchange.fetch_positions(symbols=[symbol])

            # Do something..
            #print(f"{formatted_time} - Iteration {Start + 1}")
            
            long = 0
            short = 0

            for i in positions:
                symbol_name = i["info"]['symbol']
                side = i['side']
                tradeamount = i["info"]['isolatedMargin']
                position_amt = float(i["info"]['positionAmt'])
                tradeqty = abs(position_amt)
                entryPrice = i["info"]['entryPrice']
                markPrice = i["info"]['markPrice']
                Pnl = i["info"]['unRealizedProfit']
                #ROE = i['percentage']
                ROE = round(float(i['percentage']), 2)    

                if side == 'long':
                    long = 1
                    short = 2
                elif side == 'short':
                    long = 2
                    short = 1
            
            #print(f"{formatted_time} - S_ROE_{Start + 1}: {ROE}")    
            

            if (long == 1 or short == 1):
                print(f"{formatted_time} - S_ROE_{Start + 1}: {ROE}")

            #print(type(ROE), ROE)

            # Short Position Size Management
            if short == 1 and ROE < 8 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
                
                # Create flag file
                flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"
                open(flag_file, 'w').close()

            #Reset Short Position Size    
            if short == 1 and ROE > 13 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")

                Adjust_S_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty_2)
                
                # Send detailed Telegram message
                message = (f"✅ *{Coin} SHORT POSITION SIZE RESET*\n"
                   f"• Time: {current_time}\n"
                   f"• ROE: {ROE:.2f}%\n"
                   f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                   f"• Reason: CheckPoint 8%\n"
                   f"• Reason: ROE above 13%\n\n"
                   f"• Position Reversal Found, recovered 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - Short Size Reset")


            # Short Position Size Management
            if long == 1 and ROE < 8 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):

                # Create flag file
                flag_file = f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"
                open(flag_file, 'w').close()

            #Reset Long Position Size
            if long == 1 and ROE > 13 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                # Remove flag file if exists
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_40.txt")
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")            
                
                Adjust_b_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty_2)
                
                # Send detailed Telegram message
                message = (f"✅ *{Coin} LONG POSITION SIZE RESET*\n"
                           f"• Time: {current_time}\n"
                           f"• ROE: {ROE:.2f}%\n"
                           f"• Entry Price: ${entryPrice}\n"  # Optional: format to 2 decimal places
                           f"• Reason: CheckPoint 8%\n"
                           f"• Reason: ROE above 13%\n\n"
                           f"• Position Reversal Found, recovered 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - Long Size Reset")
            
            #Close positions after decline from checkpoint 8 
            if (long == 1) and ROE < -5 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                # Close buy order
                sorder1 = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=tradeqty)
                message = (f"🔄 *{Coin} Long Position Closed*\n\n"
                           f"📊 *Position Details:*\n"
                           f"• Time: {current_time}\n"
                           f"• Entry Price: ${price}\n"
                           f"• Quantity: {qty}\n"
                           f"• Return on Equity (ROE): {ROE:.2f}%\n"
                           f"• Profit & Loss (PNL): ${float(Pnl):.2f}\n"
                           f"• Reason: ROE decreased below -5%\n"
                           f"• ROE declined from the checkpoint of 8% after 23% reduction monitoring period.\n")

                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)        
                print(f"{formatted_time} - Close buy Order")
                time.sleep(2)

                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")

                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")                      

            if (short == 1) and ROE < -5 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                # Close Sell order
                bborder1 = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=tradeqty) 
                message = (f"🔄 *{Coin} Short Position Closed*\n\n"
                           f"📊 *Position Details:*\n"
                           f"• Time: {current_time}\n"
                           f"• Entry Price: ${price}\n"
                           f"• Quantity: {qty}\n"
                           f"• Return on Equity (ROE): {ROE:.2f}%\n"
                           f"• Profit & Loss (PNL): ${float(Pnl):.2f}\n"
                           f"• Reason: ROE decreased below -5%\n"
                           f"• ROE declined from the checkpoint of 8% after 23% reduction monitoring period.\n")

                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)        
                print(f"{formatted_time} - Close Sell Order")

                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_8.txt")                

                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_reduction_40.txt")  
            

            # Wait for 30 seconds
            time.sleep(20)            
            # Increment the Start
            Start += 1
