#!/usr/bin/env python3
"""
Test 60% Flag Cleanup
Tests the new 60% flag cleanup functionality added by the user.
"""

import os
import time
from datetime import datetime

# Test Configuration
TEST_COIN = "WIFUSDT"
LOG_DIR = "./test_logs"

# Create test log directory
os.makedirs(LOG_DIR, exist_ok=True)

class Flag60CleanupTester:
    def __init__(self):
        self.test_results = []
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def flag_exists(self, flag_name):
        return os.path.exists(f"{LOG_DIR}/{TEST_COIN}_{flag_name}.txt")
    
    def create_flag(self, flag_name):
        with open(f"{LOG_DIR}/{TEST_COIN}_{flag_name}.txt", 'w') as f:
            f.write("")
        self.log(f"Created flag: {flag_name}")
    
    def remove_flag(self, flag_name):
        flag_path = f"{LOG_DIR}/{TEST_COIN}_{flag_name}.txt"
        if os.path.exists(flag_path):
            os.remove(flag_path)
            self.log(f"Removed flag: {flag_name}")
    
    def cleanup_all_flags(self):
        """Clean up all flag files"""
        flag_patterns = [
            "tmp_flag_60", "reduction_60", "tmp_flag_40", "tmp_flag_80", "tmp_flag_120",
            "reduction_40", "reduction_80", "reduction_120", "tmp_flag_long", "tmp_flag_short"
        ]
        for pattern in flag_patterns:
            self.remove_flag(pattern)

    def simulate_new_buy_signal_cleanup(self):
        """Test 60% flag cleanup in New Buy Position signal"""
        self.log("🧪 Testing New Buy Signal - 60% Flag Cleanup", "TEST")
        
        # Create 60% flags to test cleanup
        self.create_flag("tmp_flag_60")
        self.create_flag("reduction_60")
        
        # Simulate new buy signal cleanup (based on your code lines 59-62)
        flags_to_check = ["tmp_flag_60", "reduction_60"]
        
        for flag in flags_to_check:
            if self.flag_exists(flag):
                self.remove_flag(flag)
        
        # Verify cleanup
        cleanup_success = True
        for flag in flags_to_check:
            if self.flag_exists(flag):
                self.log(f"❌ Flag {flag} not cleaned up!", "ERROR")
                cleanup_success = False
        
        if cleanup_success:
            self.log("✅ New Buy Signal: 60% flags cleaned up correctly", "SUCCESS")
            self.test_results.append("PASS")
        else:
            self.log("❌ New Buy Signal: 60% flag cleanup failed", "FAILURE")
            self.test_results.append("FAIL")

    def simulate_new_sell_signal_cleanup(self):
        """Test 60% flag cleanup in New Sell Position signal"""
        self.log("🧪 Testing New Sell Signal - 60% Flag Cleanup", "TEST")
        
        # Create 60% flags to test cleanup
        self.create_flag("tmp_flag_60")
        self.create_flag("reduction_60")
        
        # Simulate new sell signal cleanup (based on your code lines 115-118)
        flags_to_check = ["tmp_flag_60", "reduction_60"]
        
        for flag in flags_to_check:
            if self.flag_exists(flag):
                self.remove_flag(flag)
        
        # Verify cleanup
        cleanup_success = True
        for flag in flags_to_check:
            if self.flag_exists(flag):
                self.log(f"❌ Flag {flag} not cleaned up!", "ERROR")
                cleanup_success = False
        
        if cleanup_success:
            self.log("✅ New Sell Signal: 60% flags cleaned up correctly", "SUCCESS")
            self.test_results.append("PASS")
        else:
            self.log("❌ New Sell Signal: 60% flag cleanup failed", "FAILURE")
            self.test_results.append("FAIL")

    def simulate_position_switch_cleanup(self):
        """Test 60% flag cleanup in position switching"""
        self.log("🧪 Testing Position Switch - 60% Flag Cleanup", "TEST")
        
        # Test Close Long and Open Short (lines 175-178)
        self.create_flag("tmp_flag_60")
        self.create_flag("reduction_60")
        
        # Simulate cleanup
        flags_to_check = ["tmp_flag_60", "reduction_60"]
        for flag in flags_to_check:
            if self.flag_exists(flag):
                self.remove_flag(flag)
        
        # Verify
        cleanup_success = True
        for flag in flags_to_check:
            if self.flag_exists(flag):
                cleanup_success = False
        
        if cleanup_success:
            self.log("✅ Close Long and Open Short: 60% flags cleaned up", "SUCCESS")
            self.test_results.append("PASS")
        else:
            self.log("❌ Close Long and Open Short: cleanup failed", "FAILURE")
            self.test_results.append("FAIL")
        
        # Test Close Short and Open Long (lines 265-268)
        self.create_flag("tmp_flag_60")
        self.create_flag("reduction_60")
        
        # Simulate cleanup
        for flag in flags_to_check:
            if self.flag_exists(flag):
                self.remove_flag(flag)
        
        # Verify
        cleanup_success = True
        for flag in flags_to_check:
            if self.flag_exists(flag):
                cleanup_success = False
        
        if cleanup_success:
            self.log("✅ Close Short and Open Long: 60% flags cleaned up", "SUCCESS")
            self.test_results.append("PASS")
        else:
            self.log("❌ Close Short and Open Long: cleanup failed", "FAILURE")
            self.test_results.append("FAIL")

    def simulate_take_profit_cleanup(self):
        """Test 60% flag cleanup in Take Profit sections"""
        self.log("🧪 Testing Take Profit - 60% Flag Cleanup", "TEST")
        
        # Test Take Profit - Short Position (lines 359-362)
        self.create_flag("tmp_flag_60")
        self.create_flag("reduction_60")
        
        flags_to_check = ["tmp_flag_60", "reduction_60"]
        for flag in flags_to_check:
            if self.flag_exists(flag):
                self.remove_flag(flag)
        
        cleanup_success = True
        for flag in flags_to_check:
            if self.flag_exists(flag):
                cleanup_success = False
        
        if cleanup_success:
            self.log("✅ Take Profit Short: 60% flags cleaned up", "SUCCESS")
            self.test_results.append("PASS")
        else:
            self.log("❌ Take Profit Short: cleanup failed", "FAILURE")
            self.test_results.append("FAIL")
        
        # Test Take Profit - Long Position (lines 412-415)
        self.create_flag("tmp_flag_60")
        self.create_flag("reduction_60")
        
        for flag in flags_to_check:
            if self.flag_exists(flag):
                self.remove_flag(flag)
        
        cleanup_success = True
        for flag in flags_to_check:
            if self.flag_exists(flag):
                cleanup_success = False
        
        if cleanup_success:
            self.log("✅ Take Profit Long: 60% flags cleaned up", "SUCCESS")
            self.test_results.append("PASS")
        else:
            self.log("❌ Take Profit Long: cleanup failed", "FAILURE")
            self.test_results.append("FAIL")

    def test_comprehensive_scenario(self):
        """Test a comprehensive scenario with 60% checkpoint"""
        self.log("🧪 Testing Comprehensive 60% Scenario", "TEST")
        
        # Scenario: Position reaches 60% checkpoint, then new signal comes
        self.log("Step 1: Create 60% checkpoint flags")
        self.create_flag("tmp_flag_60")
        self.create_flag("reduction_60")
        
        # Verify flags exist
        if self.flag_exists("tmp_flag_60") and self.flag_exists("reduction_60"):
            self.log("✅ 60% checkpoint flags created successfully")
        else:
            self.log("❌ Failed to create 60% checkpoint flags")
            self.test_results.append("FAIL")
            return
        
        self.log("Step 2: New signal triggers cleanup")
        # Simulate new signal cleanup
        flags_to_clean = ["tmp_flag_60", "reduction_60"]
        for flag in flags_to_clean:
            if self.flag_exists(flag):
                self.remove_flag(flag)
        
        # Verify all flags are cleaned
        remaining_flags = []
        for flag in flags_to_clean:
            if self.flag_exists(flag):
                remaining_flags.append(flag)
        
        if not remaining_flags:
            self.log("✅ Comprehensive scenario: All 60% flags cleaned up", "SUCCESS")
            self.test_results.append("PASS")
        else:
            self.log(f"❌ Comprehensive scenario: Flags remaining: {remaining_flags}", "FAILURE")
            self.test_results.append("FAIL")

    def run_all_tests(self):
        """Run all 60% flag cleanup tests"""
        self.log("🚀 Starting 60% Flag Cleanup Tests")
        self.log("=" * 80)
        
        # Clean start
        self.cleanup_all_flags()
        
        # Run tests
        self.simulate_new_buy_signal_cleanup()
        self.simulate_new_sell_signal_cleanup()
        self.simulate_position_switch_cleanup()
        self.simulate_take_profit_cleanup()
        self.test_comprehensive_scenario()
        
        # Final summary
        self.log("\n" + "=" * 80)
        self.log("🏁 60% FLAG CLEANUP TEST SUMMARY", "SUMMARY")
        
        passed = self.test_results.count("PASS")
        failed = self.test_results.count("FAIL")
        total = len(self.test_results)
        
        self.log(f"Tests passed: {passed}/{total}")
        self.log(f"Tests failed: {failed}/{total}")
        self.log(f"Success rate: {(passed/total)*100:.1f}%")
        
        if failed == 0:
            self.log("🎉 ALL 60% FLAG CLEANUP TESTS PASSED!", "SUCCESS")
            self.log("✅ Your 60% flag cleanup implementation is working perfectly!")
        else:
            self.log("⚠️ Some 60% flag cleanup tests failed", "WARNING")
        
        # Cleanup
        self.cleanup_all_flags()
        self.log("🧹 Cleaned up all test flags")

if __name__ == "__main__":
    tester = Flag60CleanupTester()
    tester.run_all_tests()
